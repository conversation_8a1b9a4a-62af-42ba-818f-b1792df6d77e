package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * PCWP机构表实体类
 */
@Data
public class PcwpOrgDTO {
    /**
     * 主键（PCWP机构ID）
     */
    private String orgid;

    /**
     * 机构名称
     */
    private String orgname;

    /**
     * PCWP上级机构ID
     */
    private String parentorgid;

    /**
     * 机构类型: -1:未知 1:集团 2:分公司 3:子公司 4:经理部 5:项目部 6:股份 7:事业部
     */
    private Integer orgtype;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 状态: 0: 不可用, 1: 可用
     */
    private Integer status;

    /**
     * 最后修改时间（最后更新时间）
     */
    private String lastmodifytime;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * HR的机构ID（区分大小写）
     */
    private String hrorgid;

    /**
     * HR上级机构ID（区分大小写）
     */
    private String hrparentorgid;

    /**
     * 简称
     */
    private String shortname;

    /**
     * 简码
     */
    private String shortcode;

    /**
     * 机构属性: 0: 托管, 1: 直属
     */
    private Integer orgproperty;

    /**
     * 老系统机构ID
     */
    private String oldorgid;

    /**
     * 老系统机构名称
     */
    private String oldorgname;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer mdmstate;
}