package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * 人员基本信息表实体类
 */
@Data
public class PcwpPersoninfosDTO {
    /**
     * 唯一值（主键）
     */
    private String id;

    /**
     * 人员姓名
     */
    private String pname;

    /**
     * 人员编码
     */
    private String pnumber;

    /**
     * 所属机构（主要任职机构）
     */
    private String orgnumber;

    /**
     * 性别
     */
    private String gender;

    /**
     * 部门（主要任职机构下的部门）
     */
    private String deptnumber;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 身份证号
     */
    private String idcard;

    /**
     * 目前岗位
     */
    private String gw;

    /**
     * 学历
     */
    private String xl;

    /**
     * 工作年限
     */
    private Float gznx;

    /**
     * 学制
     */
    private String xz;

    /**
     * 毕业时间
     */
    private String bysj;

    /**
     * 毕业院校
     */
    private String byyx;

    /**
     * 毕业专业
     */
    private String byzy;

    /**
     * 联系电话
     */
    private String mobile;

    /**
     * 社保编码
     */
    private String sbbm;

    /**
     * 社保缴纳单位
     */
    private String sbjndw;

    /**
     * 邮箱
     */
    private String yx;

    /**
     * 个人特长描述
     */
    private String tc;

    /**
     * 最后更新时间
     */
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer mdmstate;
}