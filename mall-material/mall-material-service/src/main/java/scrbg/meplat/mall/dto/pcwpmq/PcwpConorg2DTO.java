package scrbg.meplat.mall.dto.pcwpmq;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PCWP大经理部表实体类
 */
@Data
public class PcwpConorg2DTO extends PCWPMQ {
    /**
     * 主键（记录ID）
     */
    private String RecordId;

    /**
     * 机构ID
     */
    private String OrgId;

    /**
     * 机构名称
     */
    private String OrgName;

    /**
     * 上级机构ID(承建机构)
     */
    private String ParentOrgId;

    /**
     * 上级机构名称(承建机构)
     */
    private String ParentOrgName;

    /**
     * 创建时间
     */
    private String CreateTime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer MdmState;
}