package scrbg.meplat.mall.dto.pcwpmq;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class MqMessageDTO {
    private Integer id;

    private String jsonText; // JSON类型字段

    private String type; //  存储routingKey

    private String time; // 消息队列时间

    private String isGet; // 是否消费（0未消费，1消费）

    private int patch; // 数据片段序列号

    private Long deliveryTag; // 消息传送标识

}
