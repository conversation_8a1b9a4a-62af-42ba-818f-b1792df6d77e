package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * 执业资格证书/上岗证信息表实体类
 */
@Data
public class PcwpZyzginfosDTO {
    /**
     * 主键（唯一值）
     */
    private String id;

    /**
     * 人员姓名（冗余存储）
     */
    private String pname;

    /**
     * 人员编码（关联人员主表）
     */
    private String pnumber;

    /**
     * 执业资格类别名称（如：一级建造师/注册建筑师/安全员）
     */
    private String zyzgzsmc;

    /**
     * 资格等级（如：一级/二级/高级）
     */
    private String zydj;

    /**
     * 证书编号（官方颁发唯一编号）
     */
    private String zsnumber;

    /**
     * 发证日期（证书生效时间）
     */
    private String fzdate;

    /**
     * 最后更新时间（系统自动维护）
     */
    private String lastUpdateTime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer mdmState;
}
