package scrbg.meplat.mall.vo.stockManage;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@HeadRowHeight(40)
@ColumnWidth(40)
@ContentRowHeight(30)
@ContentFontStyle(fontHeightInPoints=16)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class OutboundSettlementManageVO {

    @ExcelProperty(value = "业务类型")
    private String supplierType;

    @ExcelProperty(value = "出库时间")
    private Date outboundTime;

    @ExcelProperty(value = "收货单位")
    private String purchasingOrgName;

    @ExcelProperty(value = "供货单位")
    private String supplierName;

    @ExcelProperty(value = "合同编号")
    private String contractNo;


    @ExcelProperty(value = "出库方式")
    private String outboundType;


    @ExcelProperty(value = "销售含税总价")
    private BigDecimal xsRateAmount;

    @ExcelProperty(value = "销售不含税总价")
    private BigDecimal xsNoRateAmount;

    @ExcelProperty(value = "采购含税进价")
    private BigDecimal cgRateAmount;

    @ExcelProperty(value = "采购不含税进价")
    private BigDecimal cgNoRateAmount;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "项目地址")
    private String projectAddress;

    @ExcelProperty(value = "数量")
    private BigDecimal num;
    @ExcelProperty(value = "期数")
    private String periodsNum;
    @ApiModelProperty(value = "受票单位")
    private String ticketReceivingUnit;

    @ApiModelProperty(value = "受票单位地址")
    private String ticketReceivingUnitAddress;

    @ApiModelProperty(value = "受票单位税号")
    private String ticketReceivingUnitTaxNo;

    @ApiModelProperty(value = "受票单位电话")
    private String ticketReceivingUnitPhone;

    @ApiModelProperty(value = "受票单位开户行")
    private String ticketReceivingUnitBank;

    @ApiModelProperty(value = "受票单位账号")
    private String ticketReceivingUnitAccount;

    @ApiModelProperty(value = "发货人")
    private String receiveName;

    @ApiModelProperty(value = "发货人手机号")
    private String receivePhone;

}
