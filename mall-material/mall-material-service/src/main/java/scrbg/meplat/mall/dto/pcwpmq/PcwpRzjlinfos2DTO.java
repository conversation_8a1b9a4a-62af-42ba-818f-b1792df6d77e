package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * 任职项目经历表实体类
 */
@Data
public class PcwpRzjlinfos2DTO extends PCWPMQ {
    /**
     * 主键（唯一值）
     */
    private String id;

    /**
     * 人员姓名
     */
    private String pname;

    /**
     * 人员编码（关联人员主表）
     */
    private String pnumber;

    /**
     * 任职形式（如：正式/兼职/借调）
     */
    private String rzxs;

    /**
     * 任职岗位（如：项目经理/技术负责人）
     */
    private String rzgw;

    /**
     * 部门编码（关联部门表）
     */
    private String bmbm;

    /**
     * 部门名称
     */
    private String bmmc;

    /**
     * 项目名称（冗余存储）
     */
    private String xmmc;

    /**
     * 项目编码（关联项目表）
     */
    private String xmbm;

    /**
     * 生效时间（任职开始日期）
     */
    private String StartDate;

    /**
     * 失效时间（任职结束日期，空表示仍在职）
     */
    private String EndDate;

    /**
     * 最后更新时间（系统自动维护）
     */
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer MdmState;
}
