package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 个人获奖信息表实体类
 */
@Data
public class PcwpGrhjinfos2DTO extends PCWPMQ {
    /**
     * 主键（唯一值）
     */
    private String id;

    /**
     * 人员姓名
     */
    private String pname;

    /**
     * 人员编码
     */
    private String pnumber;

    /**
     * 奖项名称
     */
    private String hjmc;

    /**
     * 获奖时间
     */
    private String hjsj;

    /**
     * 颁发单位
     */
    private String bfdw;

    /**
     * 最后更新时间
     */
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer MdmState;
}