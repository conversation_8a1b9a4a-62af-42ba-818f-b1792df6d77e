package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * PCWP项目表实体类（核心基建项目管理）
 */
@Data
public class PcwpProject2DTO extends PCWPMQ {
    private String ProjectID;
    private String ProjectNo;
    private String AbbreviationName;
    private String ProjectName;
    private String StartDate;
    private String EndDate;
    private Float TotalMonth;
    private String ProptypeId1;
    private String ProptypeName1;
    private String ProptypeId2;
    private String ProptypeIdName2;
    private String ClassId;
    private String ProjectClass;
    private String GradeId;
    private String Gread;
    private String BOwnerId;
    private String BOwnerName;
    private String ConstructionTypeId;
    private String ConstructionType;
    private String DesignerId;
    private String DesignerName;
    private String SupervisorId;
    private String SupervisorName;
    private Float BeginCol;
    private Float EndCol;
    private Float Long;
    private Float Width;
    private String Address;
    private String CreateDate;
    private String OrgId;
    private String OrgName;
    private String AgentId;
    private String AgentName;
    private String Remark;
    private String AttachId;
    private Integer State;
    private String RecorderId;
    private String RecorderName;
    private String RecorderTime;
    private String LastModifierId;
    private String LastModifier;
    private String LastModifyTime;
    private String LastAuditorId;
    private String LastAuditor;
    private String LastAuditTime;
    private String WorkId;
    private String OldWorkId;
    private Float ContractAmount;
    private String ContractId;
    private String ContractorId;
    private String Contractor;
    private Integer OldKeyId;
    private Float BudgetCost;
    private String CompletedTime;
    private String FinishedTime;
    private Integer IsFinishedEvaluation;
    private String FinishedEvaluationTime;
    private Integer MdmState;
}