package scrbg.meplat.mall.pcwp.auth.model;

import lombok.Data;
import java.util.List;

@Data
public class SupplierData {
    private List<QualificationInfo> qualificationinfos;
    private Supplier supplier;
}

@Data
class QualificationInfo {
    private String certNo;
    private String certTypeKey;
    private String certTypeValue;
    private Integer changeType;
    private String dialogImage;
    private String endDate;
    private String fileName;
    private Integer fileSize;
    private String fileUploaderId;
    private String fileUploaderName;
    private String founderId;
    private String founderName;
    private String id;
    private Integer isOverdue;
    private Integer isTransfer;
    private String objectPath;
    private String outerBillId;
    private String outerName;
    private String outerTypeKey;
    private String outerTypeValue;
    private String qualificationLevelId;
    private String qualificationLevelName;
    private String relationId;
    private Integer size;
    private String startDate;
}

@Data
class Supplier {
    private String billDate;
    private String businessScopeId;
    private String businessScopeName;
    private Integer changeState;
    private String contactName;
    private String contactTel;
    private String creditCode;
    private Integer creditLevel;
    private String deputy;
    private String email;
    private String fax;
    private String founderId;
    private String founderName;
    private String gmtCreate;
    private String gmtModified;
    private String id;
    private Integer isAbroadUnit;
    private Integer isEnabled;
    private Integer isQualified;
    private Integer isTransfer;
    private String kdId;
    private String manageOrgId;
    private String manageOrgName;
    private String nullifyCreated;
    private String nullifyCreator;
    private String nullifyCreatorId;
    private String nullifyDescription;
    private String nullifyReason;
    private String orgId;
    private String orgName;
    private String postCode;
    private String projectLeader;
    private String qualificationLevelId;
    private String qualificationLevelName;
    private Integer quarterEvaluateCount;
    private String registerAddCity;
    private String registerAddDistrict;
    private String registerAddInfo;
    private String registerAddProvince;
    private Integer registerCapital;
    private String remark;
    private Integer source;
    private String sourceId;
    private Integer state;
    private String supplierId;
    private String supplierName;
    private String supplierNo;
    private String supplierTimestamp;
    private String supplierTypeKey;
    private String supplierTypeValue;
    private Integer supplierVersion;
    private String taxTypeName;
    private String taxTypeValue;
    private String workId;
}