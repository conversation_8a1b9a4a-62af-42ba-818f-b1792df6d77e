package scrbg.meplat.mall.pcwp.third;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpPageRes;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.auth.model.SupplierData;
import scrbg.meplat.mall.pcwp.third.model.Material;
import scrbg.meplat.mall.pcwp.third.model.MaterialPageDto;
/**
 * 第三方接口服务
 */
@FeignClient(name = "pcwp-thirdapi-service", url = "${mall.prodPcwp2Url02}", configuration=FeignConfig.class)
//@Profile("!mock-pcwp")
public interface PcwpThirdApiClient extends PcwpClient{

    /**
     * 通过分页加载物资信息（提供给物资采购平台）
     * @param materialPageDto
     * @return
     */
    @PostMapping("thirdapi/matarialpurchase/queryPageMaterialDtl")
    PcwpPageRes<Material> queryPageMaterialDtl(@RequestBody MaterialPageDto materialPageDto,
                                                @RequestHeader("token") String token,
                                                @RequestHeader("syscode") String syscode);

    /**
     * 第三方接口服务-新增-修改 供应商
     * @param supplierData
     * @return
     */
    @PostMapping("/thirdapi/material/pushByShop")
    PcwpRes<String> pushByShop(@RequestBody SupplierData supplierData,
                            @RequestHeader("token") String token);
}
