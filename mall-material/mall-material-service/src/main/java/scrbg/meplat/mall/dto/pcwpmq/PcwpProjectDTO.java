package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * PCWP项目表实体类（核心基建项目管理）
 */
@Data
public class PcwpProjectDTO {
    // ==================== 项目基础信息 ====================
    /**
     * 主键（工程概况ID）
     */
    private String projectId;

    /**
     * 项目编号（唯一业务编码）
     */
    private String projectNo;

    /**
     * 项目简称（日常使用名称）
     */
    private String abbreviationName;

    /**
     * 项目全称（正式名称）
     */
    private String projectName;

    // ==================== 时间信息 ====================
    /**
     * 计划开工日期（需大于当前日期）
     */
    private String startDate;

    /**
     * 计划竣工日期（需大于开工日期）
     */
    private String endDate;

    /**
     * 项目工期（单位：月，自动计算）
     */
    private Float totalMonth;

    // ==================== 项目属性 ====================
    /**
     * 项目属性1编码（分类维度1）
     */
    private String propTypeId1;

    /**
     * 项目属性1名称（如：基建/市政/房建）
     */
    private String propTypeName1;

    /**
     * 项目属性2编码（分类维度2）
     */
    private String propTypeId2;

    /**
     * 项目属性2名称（如：政府项目/商业项目）
     */
    private String propTypeName2;

    // ==================== 工程分类 ====================
    /**
     * 工程类别ID（关联工程分类字典）
     */
    private String classId;

    /**
     * 工程类别名称（如：特大桥/隧道/五星级酒店）
     */
    private String projectClass;

    /**
     * 工程等级ID（关联等级字典）
     */
    private String gradeId;

    /**
     * 工程等级（如：一级/二级/特级）
     */
    private String grade;

    // ==================== 关联单位 ====================
    /**
     * 业主单位ID（关联业主表）
     */
    private String ownerId;

    /**
     * 业主单位名称
     */
    private String ownerName;

    /**
     * 设计单位ID（关联设计单位表）
     */
    private String designerId;

    /**
     * 设计单位名称
     */
    private String designerName;

    /**
     * 监理单位ID（关联监理单位表）
     */
    private String supervisorId;

    /**
     * 监理单位名称
     */
    private String supervisorName;

    // ==================== 工程特征 ====================
    /**
     * 起讫桩号(开始)（单位：米，道路工程专用）
     */
    private Float beginCol;

    /**
     * 起讫桩号(结束)（单位：米）
     */
    private Float endCol;

    /**
     * 总长度（单位：米，自动计算=endCol-beginCol）
     */
    private Float length;

    /**
     * 标准宽度（单位：米）
     */
    private Float width;

    /**
     * 项目详细地址（结构化地址）
     */
    private String address;

    // ==================== 管理信息 ====================
    /**
     * 创建日期（系统自动记录）
     */
    private String createDate;

    /**
     * 承建单位ID（关联机构表）
     */
    private String orgId;

    /**
     * 承建单位名称
     */
    private String orgName;

    /**
     * 经办人ID（关联人员表）
     */
    private String agentId;

    /**
     * 经办人姓名
     */
    private String agentName;

    /**
     * 项目说明（富文本存储）
     */
    private String remark;

    /**
     * 附件ID（关联文件服务）
     */
    private String attachId;

    // ==================== 状态信息 ====================
    /**
     * 项目状态（0在建 1已交工 2已竣工 -1删除）
     */
    private Integer state;

    // ==================== 审计信息 ====================
    /**
     * 录入人ID
     */
    private String recorderId;

    /**
     * 录入人姓名
     */
    private String recorderName;

    /**
     * 录入时间
     */
    private String recorderTime;

    /**
     * 最后修改人ID
     */
    private String lastModifierId;

    /**
     * 最后修改人
     */
    private String lastModifier;

    /**
     * 最后修改日期
     */
    private String lastModifyTime;

    /**
     * 审核人ID
     */
    private String lastAuditorId;

    /**
     * 审核人
     */
    private String lastAuditor;

    /**
     * 审核时间
     */
    private String lastAuditTime;

    // ==================== 流程信息 ====================
    /**
     * 当前流程实例ID
     */
    private String workId;

    /**
     * 上一流程实例ID（用于流程回退）
     */
    private String oldWorkId;

    // ==================== 合同信息 ====================
    /**
     * 合同金额（单位：万元）
     */
    private Float contractAmount;

    /**
     * 合同ID（关联合同表）
     */
    private String contractId;

    /**
     * 总包单位ID（关联承包商表）
     */
    private String contractorId;

    /**
     * 总包单位名称
     */
    private String contractor;

    // ==================== 历史数据 ====================
    /**
     * 旧系统ID（数据迁移用）
     */
    private Integer oldKeyId;

    // ==================== 成本信息 ====================
    /**
     * 预算成本（单位：万元）
     */
    private Float budgetCost;

    // ==================== 项目里程碑 ====================
    /**
     * 实际完工日期
     */
    private String completedTime;

    /**
     * 实际竣工日期
     */
    private String finishedTime;

    // ==================== 考评信息 ====================
    /**
     * 是否完成项目经理考评（0未完成 1已完成）
     */
    private Integer isFinishedEvaluation;

    /**
     * 项目经理考评完成时间
     */
    private String finishedEvaluationTime;

    // ==================== 数据状态 ====================
    /**
     * MDM数据状态（-1删除 0新增 1修改）
     */
    private Integer mdmState;
}