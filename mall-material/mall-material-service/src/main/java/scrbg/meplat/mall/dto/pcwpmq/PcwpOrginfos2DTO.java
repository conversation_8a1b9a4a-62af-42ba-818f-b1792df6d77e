package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;


/**
 * HR机构表实体类
 */
@Data
public class PcwpOrginfos2DTO extends PCWPMQ {
    /**
     * 组织内码（主键，组织ID，区分大小写）
     */
    private String id;

    /**
     * 组织名称
     */
    private String name;

    /**
     * 组织编号
     */
    private String number;

    /**
     * 父级id（区分大小写）
     */
    private String parentid;

    /**
     * 类型（O：组织、D：部门）
     */
    private String type;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * 上次更新时间（最后更新时间）
     */
    private String fLastUpdateTime;

    /**
     * 组织层级ID
     */
    private String orgLayerTypeid;

    /**
     * 组织层级编码（1:集团 3:公司 4:站/办事处/事业部 7:项目部归集 8:集团直管大经理部 9:分子公司自管项目部 11:部门）
     */
    private String orgLayerTypenumber;

    /**
     * 组织层级名称
     */
    private String orgLayerTypeName;

    /**
     * 项目部所属大经理部内码
     */
    private String djlbid;

    /**
     * 项目部所属大经理部编码
     */
    private String djlbnumber;

    /**
     * 项目部所属大经理部名称
     */
    private String djlbname;

    /**
     * 上级法定组织
     */
    private String glzzid;

    /**
     * 上级法定组织编码
     */
    private String glzznumber;

    /**
     * 上级法定组织名称
     */
    private String glzzname;

    /**
     * 排序码
     */
    private String sortCode;

    /**
     * 是否启用（0是启用，1 是停用）
     */
    private Integer isSealUp;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer MdmState;
}