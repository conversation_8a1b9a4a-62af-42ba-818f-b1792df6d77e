package scrbg.meplat.mall.dto.pcwpmq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * PCWP大经理部表实体类
 */
@Data
public class PcwpConorgDTO {
    /**
     * 主键（记录ID）
     */
    private String recordid;

    /**
     * 机构ID
     */
    private String orgid;

    /**
     * 机构名称
     */
    private String orgname;

    /**
     * 上级机构ID(承建机构)
     */
    private String parentorgid;

    /**
     * 上级机构名称(承建机构)
     */
    private String parentorgname;

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer mdmstate;
}