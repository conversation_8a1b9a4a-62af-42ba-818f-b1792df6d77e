package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * 统一身份认证账户表实体类
 */
@Data
public class PcwpTtAccountDTO {
    /**
     * 主键（用户ID，唯一标识）
     */
    private String id;

    /**
     * 用户姓名（中文全名）
     */
    private String name;

    /**
     * 用户登录名（系统唯一，用于登录认证）
     */
    private String username;

    /**
     * 员工工号（企业唯一编号，关联HR系统）
     */
    private String employeeNumber;

    /**
     * 账户创建时间（系统自动记录）
     */
    private String created;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer mdmState;
}
