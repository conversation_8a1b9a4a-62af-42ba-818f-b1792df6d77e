package scrbg.meplat.mall.dto.pcwpmq;

import lombok.Data;

/**
 * 职称信息表实体类
 */
@Data
public class PcwpZcinfos2DTO extends PCWPMQ {
    /**
     * 主键（唯一值）
     */
    private String id;

    /**
     * 人员姓名（与人事主表关联）
     */
    private String pname;

    /**
     * 人员编码（关联人员主键）
     */
    private String pnumber;

    /**
     * 职称类别名称（如：工程师/教授/研究员）
     */
    private String zclbmc;

    /**
     * 职称等级（如：初级/中级/高级）
     */
    private String zcdj;

    /**
     * 专业分类（行业标准分类）
     */
    private String zyfl;

    /**
     * 专业名称（具体专业方向）
     */
    private String zymc;

    /**
     * 证书编号（官方颁发编号）
     */
    private String zsnumber;

    /**
     * 发证日期（证书生效时间）
     */
    private String fzdate;

    /**
     * 最后更新时间（系统自动维护）
     */
    private String lastupdatetime;

    /**
     * MDM数据状态: -1: 删除数据, 0: 新增数据, 1: 修改数据
     */
    private Integer MdmState;
}
