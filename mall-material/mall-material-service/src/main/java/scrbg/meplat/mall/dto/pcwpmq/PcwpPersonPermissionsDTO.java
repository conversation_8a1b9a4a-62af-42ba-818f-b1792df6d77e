package scrbg.meplat.mall.dto.pcwpmq;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * PCWP云中心用户权限表实体类
 */
@Data
public class PcwpPersonPermissionsDTO {
    /**
     * 用户ID（关联用户表）
     */
    private String userId;

    /**
     * 用户姓名（冗余存储）
     */
    private String userName;

    /**
     * 组织机构ID（关联机构表）
     */
    private String orgId;

    /**
     * 组织机构名称（冗余存储）
     */
    private String orgName;

    /**
     * 机构简码（用于快速检索）
     */
    private String shortCode;

    /**
     * 角色ID（关联角色表）
     */
    private String roleId;

    /**
     * 角色名称（冗余存储）
     */
    private String roleName;
}