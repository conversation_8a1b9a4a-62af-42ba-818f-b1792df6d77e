package scrbg.meplat.mall.pcwp.third;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.pcwp.FeignConfig;
import scrbg.meplat.mall.pcwp.PcwpClient;
import scrbg.meplat.mall.pcwp.PcwpRes;

import java.util.Map;

/**
 * 第三方物资验收接口服务
 */
@FeignClient(name = "pcwp-thirdapi-reconciliation-service", url = "${mall.prodPcwp2Url02}", configuration = FeignConfig.class)
@Profile("!mock-pcwp")
public interface PcwpThirdApiCloudCenterClient extends PcwpClient {
    /**
     * PCWP2.0-组织机构服务
     *获取用户的所有角色
     * @param params 组织ID
     * {
     *   "userName": "",  用户姓名
     *   "page": 1,        页数
     *   "limit": 50,      单页上限
     *   "orgId": "",      组织机构id
     *   "orgName": "",    组织机构名称
     *   "userId": ""      用户id
     * }
     */
    @PostMapping("/thirdapi/hr/role/getUserRole")
    PcwpRes<Map<String, Object>> getPersonPermissons(@RequestBody Map<String,Object> params,
                                                        @RequestHeader(value = "token") String token,
                                                        @RequestHeader(value = "sysCode") String sysCode,
                                                        @RequestHeader(value = "org") String org);
}
